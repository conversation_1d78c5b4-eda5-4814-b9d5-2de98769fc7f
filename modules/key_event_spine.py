# modules/key_event_spine.py
"""
关键事件脊柱提取模块

主要功能：
1. 从章节摘要中提取关键事件脊柱
2. 为每个事件分配重要性和紧张度权重
3. 支持动态阶段切分
"""

import json
import re
from typing import List, Dict, Any, Optional
from config import logger
from modules.langchain_interface import call_llm_json_response


def extract_spine(chapter_summary: Dict[str, Any], alias_map: Dict[str, str]) -> List[Dict[str, Any]]:
    """
    从章节摘要中提取关键事件脊柱

    Args:
        chapter_summary: 章节摘要数据
        alias_map: 角色别名映射

    Returns:
        List[Dict]: 关键事件列表，每个事件包含importance和tension权重
    """
    try:
        # 构建LLM请求数据
        request_data = {
            "chapter_summary": chapter_summary,
            "alias_map": alias_map,
            "max_events": 4  # 每章最多4个关键事件
        }

        # 调用LLM提取关键事件
        response = call_llm_json_response(
            api_function="extract_spine_events",
            prompt_data=request_data,
            using_cache=True
        )

        if not response or "spine_events" not in response:
            logger.warning("LLM未返回有效的spine_events，使用fallback逻辑")
            return _fallback_extract_spine(chapter_summary)

        spine_events = response["spine_events"]

        # 验证和标准化事件格式
        validated_events = []
        for event in spine_events:
            if _validate_spine_event(event):
                validated_events.append(event)
            else:
                logger.warning(f"无效的spine事件被跳过: {event}")

        # 确保至少有1个事件
        if not validated_events:
            logger.warning("没有有效的spine事件，使用fallback逻辑")
            return _fallback_extract_spine(chapter_summary)

        logger.info(f"成功提取 {len(validated_events)} 个spine事件")
        return validated_events

    except Exception as e:
        logger.error(f"提取spine事件时出错: {str(e)}")
        return _fallback_extract_spine(chapter_summary)


def _validate_spine_event(event: Dict[str, Any]) -> bool:
    """验证spine事件的格式"""
    required_fields = ["event", "importance", "tension"]

    if not isinstance(event, dict):
        return False

    for field in required_fields:
        if field not in event:
            return False

    # 验证权重值范围
    if not (0.0 <= event.get("importance", 0) <= 1.0):
        return False
    if not (0.0 <= event.get("tension", 0) <= 1.0):
        return False

    # 验证事件描述
    if not isinstance(event.get("event"), str) or not event["event"].strip():
        return False

    return True


def _fallback_extract_spine(chapter_summary: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Fallback逻辑：从章节摘要的key_events中提取spine事件
    """
    try:
        spine_events = []
        key_events = chapter_summary.get("key_events", [])

        for i, event in enumerate(key_events[:4]):  # 最多取4个事件
            if isinstance(event, dict) and "event" in event:
                spine_event = {
                    "event": event["event"],
                    "importance": 0.7,  # 默认重要性
                    "tension": 0.6 + (i * 0.1)  # 递增的紧张度
                }
                spine_events.append(spine_event)

        # 如果没有key_events，从narrative.content中提取
        if not spine_events:
            content = chapter_summary.get("narrative", {}).get("content", "")
            if content:
                spine_event = {
                    "event": content[:100] + "..." if len(content) > 100 else content,
                    "importance": 0.5,
                    "tension": 0.5
                }
                spine_events.append(spine_event)

        return spine_events

    except Exception as e:
        logger.error(f"Fallback提取spine事件时出错: {str(e)}")
        return [{
            "event": "章节内容",
            "importance": 0.5,
            "tension": 0.5
        }]


def dynamic_phase_split(summaries_with_spine: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    基于spine事件权重进行动态阶段切分

    Args:
        summaries_with_spine: 包含spine_events的章节摘要列表

    Returns:
        List[Dict]: 阶段分组信息
    """
    try:
        phases = []
        current_phase = []
        current_weight = 0.0

        for summary in summaries_with_spine:
            spine_events = summary.get("spine_events", [])

            # 计算章节权重：0.7*tension + 0.3*token_weight
            chapter_tension = sum(event.get("tension", 0) for event in spine_events) / max(len(spine_events), 1)
            token_weight = min(len(summary.get("narrative", {}).get("content", "")), 1000) / 1000.0
            chapter_weight = 0.7 * chapter_tension + 0.3 * token_weight

            # 判断是否需要拆分或合并
            if current_weight + chapter_weight > 1.2:  # 拆分阈值
                if current_phase:
                    phases.append({
                        "chapters": current_phase,
                        "weight": current_weight,
                        "action": "split"
                    })
                current_phase = [summary["basic_info"]["chapter_number"]]
                current_weight = chapter_weight
            else:
                current_phase.append(summary["basic_info"]["chapter_number"])
                current_weight += chapter_weight

        # 处理最后一个阶段
        if current_phase:
            phases.append({
                "chapters": current_phase,
                "weight": current_weight,
                "action": "final"
            })

        # 合并权重过小的阶段
        merged_phases = []
        i = 0
        while i < len(phases):
            current = phases[i]
            if current["weight"] < 0.8 and i + 1 < len(phases):  # 合并阈值
                next_phase = phases[i + 1]
                merged = {
                    "chapters": current["chapters"] + next_phase["chapters"],
                    "weight": current["weight"] + next_phase["weight"],
                    "action": "merge"
                }
                merged_phases.append(merged)
                i += 2
            else:
                merged_phases.append(current)
                i += 1

        logger.info(f"动态阶段切分完成，共 {len(merged_phases)} 个阶段")
        return merged_phases

    except Exception as e:
        logger.error(f"动态阶段切分时出错: {str(e)}")
        return []


def calculate_phase_weight(spine_events: List[Dict[str, Any]], token_count: int) -> float:
    """
    计算阶段权重

    Args:
        spine_events: 脊柱事件列表
        token_count: token数量

    Returns:
        float: 阶段权重值
    """
    if not spine_events:
        return 0.3  # 默认权重

    # 计算平均紧张度
    avg_tension = sum(event.get("tension", 0) for event in spine_events) / len(spine_events)

    # 标准化token权重
    token_weight = min(token_count, 1000) / 1000.0

    # 组合权重：70%紧张度 + 30%token权重
    phase_weight = 0.7 * avg_tension + 0.3 * token_weight

    return phase_weight
