import argparse
import os
import json
# Avoid tokenizer parallelism forks on macOS
os.environ["TOKENIZERS_PARALLELISM"] = "false"
# Disable Objective-C fork safety checks on macOS
os.environ["OBJC_DISABLE_INITIALIZE_FORK_SAFETY"] = "YES"

# Import functionalities from other modules
import video_config
import metadata_db
import phase0_splitting
import phase1_indexing
import phase2_retrieval
import pexels_downloader
import pixabay_downloader
import manual_video_importer
import asyncio # For running async downloaders
from pathlib import Path

# Helper function to normalize paths
def normalize_video_path(path):
    """Convert relative paths to absolute paths and standardize format."""
    if path is None:
        return None
    return str(os.path.abspath(os.path.expanduser(str(path))))

def main():
    # Ensure base directories and DB are set up on start
    video_config.ensure_directories()
    metadata_db.create_tables()

    parser = argparse.ArgumentParser(description="Video Search System")
    subparsers = parser.add_subparsers(dest="command", help="Available commands", required=True)

    # --- Video Downloaders ---
    parser_download = subparsers.add_parser("download_videos", help="Download videos from Pexels or Pixabay.")
    parser_download.add_argument("--source", type=str, choices=['pexels', 'pixabay', 'all'], required=True, help="Source API to download from ('pexels', 'pixabay', or 'all')")
    parser_download.add_argument("--keywords", type=str, help="Comma-separated list of keywords to search for. e.g., \"nature,city\"", default=None)
    parser_download.add_argument("--use_keywords_file", action="store_true", help=f"Use keywords from the file defined in config: {video_config.KEYWORDS_FILE_PATH}")
    parser_download.add_argument("--max_per_keyword", type=int, help="Maximum number of videos to download per keyword.", default=None)

    # --- Manual Video Importer ---
    parser_import = subparsers.add_parser("import_manual_videos", help="Import videos from the manual import directory.")
    parser_import.add_argument("--manual_dir", type=str, help=f"Optional: Specify a directory to import from, overrides config: {video_config.MANUAL_IMPORT_DIR}", default=None)

    # --- Phase 0: Process Long Video ---
    parser_split = subparsers.add_parser("process_long_video", help="Process a single long video or all videos in a directory for splitting.")
    parser_split.add_argument("--video_dir", help="Path to the directory containing long video files to be processed (e.g., data/long_videos).")
    parser_split.add_argument("--video_path", help="Path to a single long video file to be processed.")
    parser_split.add_argument("--process_all_new_in_db", action="store_true", help="Process all videos in the database that haven't been split (i.e., no clips in video_clips table referencing them). Ignores --video_dir and --video_path.")
    # parser_split.add_argument("--param_set_name", help="Name of a stored PySceneDetect parameter set to use.")

    # --- Phase 1: Build Index ---
    parser_index = subparsers.add_parser("build_index", help="Build or update feature indexes for all clips.")
    parser_index.add_argument("--video_library_path", default=video_config.CLIPS_DIR,
                              help="Path to the directory containing all video clips to be indexed.")
    parser_index.add_argument("--force", action="store_true",
                              help="Force regeneration of all indexes, even for clips that already have embeddings.")

    # --- Phase 2: Search ---
    parser_search = subparsers.add_parser("search", help="Search for videos using a text query.")
    parser_search.add_argument("--query", required=True, help="Text query to search for.")
    parser_search.add_argument("--top_n", type=int, default=video_config.TOP_K_RESULTS,
                               help="Number of top results to display.")

    args = parser.parse_args()

    if args.command == "download_videos":
        keywords_list = []
        if args.keywords:
            keywords_list.extend([kw.strip() for kw in args.keywords.split(',') if kw.strip()])

        # keywords_file_path will be resolved within downloader if args.use_keywords_file is True
        # and no specific keywords_list is passed to it.

        if not keywords_list and not args.use_keywords_file:
            print("Error: For 'download_videos', you must specify --keywords or --use_keywords_file.")
            parser_download.print_help()
            return

        print(f"Executing: Download videos from source: {args.source}")
        if args.source == 'pexels' or args.source == 'all':
            print("Starting Pexels downloads...")
            asyncio.run(pexels_downloader.main_pexels_downloader(keywords=keywords_list if keywords_list else None,
                                                                 keywords_file=video_config.KEYWORDS_FILE_PATH if args.use_keywords_file else None,
                                                                 max_videos_per_keyword=args.max_per_keyword))
            print("Pexels downloads finished.")

        if args.source == 'pixabay' or args.source == 'all':
            print("Starting Pixabay downloads...")
            asyncio.run(pixabay_downloader.main_pixabay_downloader(keywords=keywords_list if keywords_list else None,
                                                                   keywords_file=video_config.KEYWORDS_FILE_PATH if args.use_keywords_file else None,
                                                                   max_videos_per_keyword=args.max_per_keyword))
            print("Pixabay downloads finished.")

        print("Video download process complete.")

    elif args.command == "import_manual_videos":
        print("Executing: Import manual videos")
        manual_video_importer.import_manual_videos(manual_source_dir=args.manual_dir)
        print("Manual video import process complete.")

    elif args.command == "process_long_video":
        videos_to_process = []
        source_infos_map = {} # To store source_info for each video_path if available

        if args.process_all_new_in_db:
            print("Processing all new videos from the database...")
            conn = metadata_db.get_db_connection()
            cursor = conn.cursor()
            # Find long_videos that do not have any corresponding entries in video_clips
            # This implies they haven't been split or processed by phase0 yet.
            cursor.execute("""
                SELECT lv.id, lv.video_path, lv.source, lv.source_id, lv.description, lv.keywords, lv.raw_api_metadata
                FROM long_videos lv
                LEFT JOIN video_clips vc ON lv.id = vc.original_long_video_id
                WHERE vc.clip_id IS NULL
            """)
            new_videos_from_db = cursor.fetchall()
            conn.close()
            if not new_videos_from_db:
                print("No new videos found in the database that require processing.")
                return
            for row in new_videos_from_db:
                video_path = str(row['video_path'])
                if not Path(video_path).exists():
                    print(f"Warning: Video path {video_path} (ID: {row['id']}) from DB does not exist on filesystem. Skipping.")
                    continue
                videos_to_process.append(video_path)

                raw_meta_str = row['raw_api_metadata']
                api_metadata_dict = None
                if raw_meta_str:
                    try:
                        api_metadata_dict = json.loads(raw_meta_str)
                    except json.JSONDecodeError:
                        print(f"Warning: Could not parse raw_api_metadata JSON for video ID {row['id']}. Original string: {raw_meta_str[:100]}...")
                        api_metadata_dict = {} # Default to empty dict or handle as error

                source_infos_map[video_path] = {
                    'source': row['source'],
                    'source_id': row['source_id'],
                    'description': row['description'], # Updated from tags
                    'keywords': row['keywords'],       # Added (keywords from DB are space-separated strings)
                    'raw_api_metadata': api_metadata_dict # Store the parsed dict
                }
            print(f"Found {len(videos_to_process)} new video(s) from DB to process.")

        elif args.video_path:
            # Normalize the path
            args.video_path = normalize_video_path(args.video_path)

            if not os.path.exists(args.video_path):
                print(f"Error: Video path does not exist: {args.video_path}")
                return
            if not os.path.isfile(args.video_path):
                print(f"Error: --video_path must be a file: {args.video_path}")
                return
            videos_to_process.append(args.video_path)

        if args.video_dir:
            # Normalize the directory path
            args.video_dir = normalize_video_path(args.video_dir)

            if not os.path.exists(args.video_dir):
                print(f"Error: Video directory does not exist: {args.video_dir}")
                return
            if not os.path.isdir(args.video_dir):
                print(f"Error: --video_dir must be a directory: {args.video_dir}")
                return

            print(f"Scanning directory: {args.video_dir} for videos (including subdirectories)...")
            video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')

            # Use os.walk to recursively scan all subdirectories
            for root, _, files in os.walk(args.video_dir):
                # Show which directory we're currently scanning
                if root != args.video_dir:
                    rel_path = os.path.relpath(root, args.video_dir)
                    print(f"  Scanning subdirectory: {rel_path}")

                for filename in files:
                    if filename.lower().endswith(video_extensions):
                        full_path = normalize_video_path(os.path.join(root, filename))
                        videos_to_process.append(full_path)
                        # Show found video with relative path for better readability
                        rel_video_path = os.path.relpath(full_path, args.video_dir)
                        print(f"    Found video: {rel_video_path}")
            if not videos_to_process and not args.video_path: # if video_dir was given but no videos found
                 print(f"No video files (.mp4, .avi, .mov, .mkv, .flv, .wmv) found in directory: {args.video_dir}")
                 return

        if not videos_to_process:
            print("No videos found to process.")
            return

        print(f"Found {len(videos_to_process)} video(s) to process.")
        for video_file_path in videos_to_process:
            print(f"Executing: Process Long Video for '{video_file_path}'")

            # Check if the video already exists in the database with a different path
            existing_video = metadata_db.get_long_video_by_path(video_file_path)

            # For videos processed via --video_path or --video_dir, we might not have source_info
            # unless we query DB. Phase0 can handle this by creating default source_info.
            # If it came from process_all_new_in_db, source_info is populated.
            current_source_info = source_infos_map.get(video_file_path)
            long_video_db_id_override = None

            if existing_video:
                print(f"Video already exists in database with ID {existing_video['id']}")
                # Process with the existing ID
                long_video_db_id_override = existing_video['id']

                # Check if this video already has clips
                conn = metadata_db.get_db_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM video_clips WHERE original_long_video_id = ?", (long_video_db_id_override,))
                clip_count = cursor.fetchone()[0]
                conn.close()

                if clip_count > 0:
                    print(f"Video already has {clip_count} clips. Skipping processing.")
                    continue
            elif current_source_info:
                # If we have source_info, we might also have the DB ID already
                # Try to get existing DB ID to pass as override
                lv_info = metadata_db.get_long_video_info(source=current_source_info['source'], source_id=current_source_info['source_id'])
                if lv_info and lv_info.get('id'):
                    long_video_db_id_override = lv_info['id']
                    print(f"Found existing video with same source/source_id, ID: {long_video_db_id_override}")

            phase0_splitting.process_long_video(
                video_file_path,
                tuned_pyscenedetect_params=video_config.DEFAULT_PYSCENEDETECT_PARAMS,
                long_video_db_id_override=long_video_db_id_override, # Pass if known
                source_info=current_source_info # Pass full source_info dict
            )
            print(f"Finished processing: {video_file_path}")

    elif args.command == "build_index":
        force_mode = "with --force flag" if args.force else "normal mode"
        print(f"Executing: Build Index from library '{args.video_library_path}' ({force_mode})")
        phase1_indexing.process_video_library_for_indexing(args.video_library_path, force=args.force)

    elif args.command == "search":
        print(f"Executing: Search for query '{args.query}' (top {args.top_n})")
        # Load resources once for search session
        # In a real application, these might be loaded once when a server starts.
        retrieval_resources = phase2_retrieval.load_resources_for_retrieval()

        if not retrieval_resources.get('visual_index') and not retrieval_resources.get('text_index'):
             print("FAISS indexes are not loaded or are empty. Cannot perform search. Please run 'build_index' first.")
             return
        if not retrieval_resources.get('intern_video_model') or not retrieval_resources.get('sentence_model'):
             print("Query models are not loaded. Cannot perform search.")
             return

        results = phase2_retrieval.retrieve_results_for_query(
            args.query,
            retrieval_resources,
            top_n_to_display=args.top_n
        )
        if results:
            print("\nSearch Results:")
            for i, res_item in enumerate(results):
                print(f"  {i+1}. Clip ID: {res_item['clip_id']}")
                print(f"     Path: {res_item['clip_path']}")
                print(f"     Score: {res_item.get('retrieval_score', 0.0):.4f}")

                # Display new fields from metadata_db.get_clip_metadata_for_retrieval
                description = res_item.get('description', '')
                keywords = res_item.get('keywords', '') # Keywords from DB are space-separated string
                original_video_description = res_item.get('original_video_description', '')
                original_video_keywords = res_item.get('original_video_keywords', '')

                # Prefer clip-specific, fallback to original video's if clip-specific is empty
                display_description = description if description else original_video_description

                # Use keywords_display if available (processed JSON), otherwise fall back to raw keywords
                if res_item.get('keywords_display'):
                    display_keywords = res_item['keywords_display']
                else:
                    display_keywords = keywords if keywords else original_video_keywords

                if display_description:
                    print(f"     Description: {display_description[:150]}...")
                if display_keywords:
                    print(f"     Keywords: {display_keywords[:150]}...")

                # Display a snippet of the transcript
                transcript_snippet = res_item.get('transcript_text', '')
                if transcript_snippet:
                     print(f"     Transcript: {transcript_snippet[:150]}...")
                start_time = res_item.get('start_time_in_original')
                end_time = res_item.get('end_time_in_original')
                if start_time is not None and end_time is not None:
                    print(f"     Original Time: {start_time:.2f}s - {end_time:.2f}s")
                print("---")
        else:
            print("No matching video clips found.")
    else:
        parser.print_help()

if __name__ == "__main__":
    main()